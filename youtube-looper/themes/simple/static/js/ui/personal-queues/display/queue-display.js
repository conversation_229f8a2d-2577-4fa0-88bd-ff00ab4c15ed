// ============================================================================
// PERSONAL QUEUES DISPLAY COMPONENT
// ============================================================================

/**
 * Personal Queues Display Component
 * Handles queue cards rendering, visualization, and UI state management
 * Manages the display of personal queue lists and individual queue cards
 */

/**
 * Display personal queues in the UI using enhanced model data
 * @param {Array} queues - Array of personal queue data
 */
function displayPersonalQueues(queues) {
  const container = document.getElementById('personal-queues-list');
  if (!container) return;

  // Show loading state while rendering
  showLoadingState(container, 'Rendering your queues...');

  // Use setTimeout to prevent UI blocking for large queue lists
  setTimeout(() => {
    try {
      const queueHTML = queues.map(queue => createQueueCardHTML(queue)).join('');
      container.innerHTML = queueHTML;

      // Re-initialize event listeners after DOM update
      if (typeof initializeQueuePrivacyToggles === 'function') {
        initializeQueuePrivacyToggles();
      }
      if (typeof initializeQueueLinkCopyButtons === 'function') {
        initializeQueueLinkCopyButtons();
      }
      if (typeof initializeQueueCardClickHandlers === 'function') {
        initializeQueueCardClickHandlers();
      }

      console.log(`✅ Rendered ${queues.length} personal queue cards`);
    } catch (error) {
      console.error('Error rendering personal queues:', error);
      showErrorState(container, 'Failed to display queues', 'Please try refreshing the page.');
    }
  }, 10);
}

/**
 * Create HTML for a single queue card
 * @param {Object} queue - Queue data
 * @returns {string} HTML string
 */
function createQueueCardHTML(queue) {
  const thumbnailHTML = queue.thumbnail ? 
    `<img src="${escapeHtml(queue.title)}" alt="${escapeHtml(queue.title)}" loading="lazy" />` :
    '<div class="placeholder-thumbnail">🎵</div>';

  const shareSection = queue.isPublic ? `
    <div class="queue-link-section">
      <div class="queue-link-header">
        <span class="queue-link-label">Share Link</span>
        <button class="copy-queue-link-btn" data-queue-id="${queue.id}" title="Copy Queue Link">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
        </button>
      </div>
      <div class="queue-link-display">
        <span class="queue-link-text">${window.location.origin}${window.location.pathname}?q=${queue.id}</span>
      </div>
    </div>
  ` : '';

  return `
    <div class="queue-card personal-queue" data-queue-id="${queue.id}">
      <div class="queue-thumbnail">
        ${thumbnailHTML}
        <div class="queue-overlay">
          <button class="play-queue-btn" onclick="loadPersonalQueue('${queue.id}')" title="Load and play this queue">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="queue-info">
        <h3 class="queue-title" title="${escapeHtml(queue.title)}">${escapeHtml(queue.title)}</h3>
        <div class="queue-meta">
          <span class="video-count">${queue.videoCount} video${queue.videoCount === 1 ? '' : 's'}</span>
          <span class="privacy-badge ${queue.isPublic ? 'public' : 'private'}">${queue.isPublic ? 'Public' : 'Private'}</span>
        </div>
        <div class="queue-date" title="Last modified: ${queue.lastModified ? queue.lastModified.toLocaleString() : 'Unknown'}">
          ${formatRelativeTime(queue.lastModified)}
        </div>
        <div class="queue-privacy-controls">
          <label class="privacy-toggle-small">
            <input type="checkbox" class="queue-privacy-toggle" data-queue-id="${queue.id}" ${queue.isPublic ? 'checked' : ''} />
            <span class="toggle-slider-small"></span>
            <span class="toggle-label-small">Make Public</span>
          </label>
        </div>
        ${shareSection}
      </div>
      <div class="queue-actions">
        <button class="queue-action-btn delete-btn" onclick="deletePersonalQueue('${queue.id}')" title="Delete this queue permanently">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
        </button>
      </div>
    </div>
  `;
}

/**
 * Show loading state in container
 * @param {HTMLElement} container - Container element
 * @param {string} message - Loading message
 */
function showLoadingState(container, message = 'Loading...') {
  container.innerHTML = `
    <div class="queues-loading">
      <div class="loading-spinner"></div>
      <p>${escapeHtml(message)}</p>
    </div>
  `;
}

/**
 * Show error state in container
 * @param {HTMLElement} container - Container element
 * @param {string} title - Error title
 * @param {string} message - Error message
 */
function showErrorState(container, title, message) {
  container.innerHTML = `
    <div class="queues-error">
      <div class="error-icon">⚠️</div>
      <h3>${escapeHtml(title)}</h3>
      <p>${escapeHtml(message)}</p>
      <button onclick="loadPersonalQueues(true)" class="retry-btn">Try Again</button>
    </div>
  `;
}

/**
 * Show empty state in container
 * @param {HTMLElement} container - Container element
 * @param {string} title - Empty state title
 * @param {string} message - Empty state message
 */
function showEmptyState(container, title, message) {
  container.innerHTML = `
    <div class="queues-empty">
      <div class="empty-icon">🎵</div>
      <h3>${escapeHtml(title)}</h3>
      <p>${escapeHtml(message)}</p>
    </div>
  `;
}

/**
 * Show sign-in required state
 * @param {HTMLElement} container - Container element
 */
function showSignInRequiredState(container) {
  container.innerHTML = `
    <div class="queues-empty">
      <div class="empty-icon">🔐</div>
      <h3>Sign in to access your queues</h3>
      <p>Your personal queues are private and secure. Sign in with your account to view and manage them.</p>
    </div>
  `;
}

/**
 * Update queue card display after changes
 * @param {string} queueId - Queue ID to update
 * @param {Object} updates - Updates to apply
 */
function updateQueueCard(queueId, updates) {
  const queueCard = document.querySelector(`.queue-card[data-queue-id="${queueId}"]`);
  if (!queueCard) return;

  // Update title
  if (updates.title) {
    const titleElement = queueCard.querySelector('.queue-title');
    if (titleElement) {
      titleElement.textContent = updates.title;
      titleElement.title = updates.title;
    }
  }

  // Update video count
  if (updates.videoCount !== undefined) {
    const countElement = queueCard.querySelector('.video-count');
    if (countElement) {
      countElement.textContent = `${updates.videoCount} video${updates.videoCount === 1 ? '' : 's'}`;
    }
  }

  // Update thumbnail
  if (updates.thumbnail) {
    const thumbnailImg = queueCard.querySelector('.queue-thumbnail img');
    if (thumbnailImg) {
      thumbnailImg.src = updates.thumbnail;
    }
  }

  // Update last modified
  if (updates.lastModified) {
    const dateElement = queueCard.querySelector('.queue-date');
    if (dateElement) {
      dateElement.textContent = formatRelativeTime(updates.lastModified);
      dateElement.title = `Last modified: ${updates.lastModified.toLocaleString()}`;
    }
  }
}

/**
 * Refresh personal queues display
 * @param {boolean} forceRefresh - Force refresh from server
 */
function refreshPersonalQueuesDisplay(forceRefresh = false) {
  console.log('🔄 Refreshing personal queues display...');
  
  if (typeof loadPersonalQueues === 'function') {
    loadPersonalQueues(forceRefresh);
  }
}

/**
 * Get queue card element by ID
 * @param {string} queueId - Queue ID
 * @returns {HTMLElement|null} Queue card element
 */
function getQueueCardElement(queueId) {
  return document.querySelector(`.queue-card[data-queue-id="${queueId}"]`);
}

/**
 * Highlight queue card temporarily
 * @param {string} queueId - Queue ID to highlight
 * @param {string} type - Highlight type ('success', 'error', 'info')
 */
function highlightQueueCard(queueId, type = 'info') {
  const card = getQueueCardElement(queueId);
  if (!card) return;

  card.classList.add('highlighted', `highlighted-${type}`);
  
  setTimeout(() => {
    card.classList.remove('highlighted', `highlighted-${type}`);
  }, 2000);
}

/**
 * Escape HTML to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeHtml(text) {
  if (!text) return '';
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Format relative time
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted relative time
 */
function formatRelativeTime(date) {
  if (!date) return 'Unknown';
  
  const now = new Date();
  const targetDate = date instanceof Date ? date : new Date(date);
  const diffMs = now - targetDate;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;

  return targetDate.toLocaleDateString();
}

// Export functions for global access
window.displayPersonalQueues = displayPersonalQueues;
window.createQueueCardHTML = createQueueCardHTML;
window.showLoadingState = showLoadingState;
window.showErrorState = showErrorState;
window.showEmptyState = showEmptyState;
window.showSignInRequiredState = showSignInRequiredState;
window.updateQueueCard = updateQueueCard;
window.refreshPersonalQueuesDisplay = refreshPersonalQueuesDisplay;
window.highlightQueueCard = highlightQueueCard;

console.log('✅ Personal Queues Display component loaded');
