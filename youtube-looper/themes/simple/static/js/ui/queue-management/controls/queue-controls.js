// ============================================================================
// QUEUE CONTROLS COMPONENT
// ============================================================================

/**
 * Queue Controls Component
 * Handles play/pause, next/previous, and other queue control functionality
 * Manages the main playback controls for the queue
 */

/**
 * Play or pause the queue
 */
function playQueue() {
  const videoQueue = getVideoQueue();
  const player = getPlayer();
  const isPlaying = getIsPlaying();

  if (videoQueue.length === 0) {
    console.log('Cannot play empty queue');
    return;
  }

  if (!player) {
    // Create player if it doesn't exist
    createPlayer(videoQueue[0].id);
    setIsPlaying(true);
  } else {
    // Check current player state
    const playerState = player.getPlayerState();

    if (isPlaying && playerState === 1) {
      // Currently playing - pause it
      player.pauseVideo();
      setIsPlaying(false);
    } else {
      // Not playing or paused - start/resume playing
      if (playerState === 2) {
        // Paused - resume
        player.playVideo();
      } else {
        // Load current video
        player.loadVideoById(videoQueue[getCurrentVideoIndex()].id);
      }
      setIsPlaying(true);
    }
  }

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();
}

/**
 * Go to previous video in queue
 */
function previousVideo() {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();
  const player = getPlayer();

  if (videoQueue.length === 0 || currentVideoIndex <= 0) {
    console.log('Cannot go to previous video');
    return;
  }

  setCurrentVideoIndex(currentVideoIndex - 1);
  
  if (player) {
    player.loadVideoById(videoQueue[getCurrentVideoIndex()].id);
    
    // Maintain playing state
    if (getIsPlaying()) {
      player.playVideo();
    }
  }

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log(`⏮ Previous video: ${videoQueue[getCurrentVideoIndex()].title}`);
}

/**
 * Go to next video in queue
 */
function nextVideo() {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();
  const player = getPlayer();

  if (videoQueue.length === 0 || currentVideoIndex >= videoQueue.length - 1) {
    console.log('Cannot go to next video');
    return;
  }

  setCurrentVideoIndex(currentVideoIndex + 1);
  
  if (player) {
    player.loadVideoById(videoQueue[getCurrentVideoIndex()].id);
    
    // Maintain playing state
    if (getIsPlaying()) {
      player.playVideo();
    }
  }

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log(`⏭ Next video: ${videoQueue[getCurrentVideoIndex()].title}`);
}

/**
 * Jump to specific video in queue
 * @param {number} index - Index of video to jump to
 */
function jumpToVideo(index) {
  const videoQueue = getVideoQueue();
  const player = getPlayer();

  if (index < 0 || index >= videoQueue.length) {
    console.log('Invalid video index:', index);
    return;
  }

  setCurrentVideoIndex(index);
  
  if (player) {
    player.loadVideoById(videoQueue[index].id);
    
    // Start playing if queue was playing
    if (getIsPlaying()) {
      player.playVideo();
    }
  } else {
    // Create player if it doesn't exist
    createPlayer(videoQueue[index].id);
    setIsPlaying(true);
  }

  updateQueueDisplay();
  updatePlayerControls();
  saveQueueToStorage();

  // Also update the shared queue if it exists
  autoUpdateSharedQueue();

  console.log(`🎯 Jumped to video: ${videoQueue[index].title}`);
}

/**
 * Toggle shuffle mode
 */
function toggleShuffle() {
  const isShuffleEnabled = getShuffleMode();
  setShuffleMode(!isShuffleEnabled);
  
  updatePlayerControls();
  
  console.log(`🔀 Shuffle ${!isShuffleEnabled ? 'enabled' : 'disabled'}`);
}

/**
 * Toggle repeat mode
 */
function toggleRepeat() {
  const currentRepeatMode = getRepeatMode();
  let newRepeatMode;
  
  // Cycle through: off -> one -> all -> off
  switch (currentRepeatMode) {
    case 'off':
      newRepeatMode = 'one';
      break;
    case 'one':
      newRepeatMode = 'all';
      break;
    case 'all':
    default:
      newRepeatMode = 'off';
      break;
  }
  
  setRepeatMode(newRepeatMode);
  updatePlayerControls();
  
  console.log(`🔁 Repeat mode: ${newRepeatMode}`);
}

/**
 * Handle video end event
 * Automatically advance to next video based on repeat/shuffle settings
 */
function handleVideoEnd() {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();
  const repeatMode = getRepeatMode();
  const isShuffleEnabled = getShuffleMode();

  if (videoQueue.length === 0) return;

  switch (repeatMode) {
    case 'one':
      // Repeat current video
      jumpToVideo(currentVideoIndex);
      break;
      
    case 'all':
      // Go to next video, or first if at end
      if (isShuffleEnabled) {
        jumpToRandomVideo();
      } else {
        const nextIndex = currentVideoIndex >= videoQueue.length - 1 ? 0 : currentVideoIndex + 1;
        jumpToVideo(nextIndex);
      }
      break;
      
    case 'off':
    default:
      // Go to next video if available
      if (currentVideoIndex < videoQueue.length - 1) {
        if (isShuffleEnabled) {
          jumpToRandomVideo();
        } else {
          nextVideo();
        }
      } else {
        // End of queue - stop playing
        setIsPlaying(false);
        updateQueueDisplay();
        updatePlayerControls();
      }
      break;
  }
}

/**
 * Jump to random video in queue
 */
function jumpToRandomVideo() {
  const videoQueue = getVideoQueue();
  const currentVideoIndex = getCurrentVideoIndex();
  
  if (videoQueue.length <= 1) {
    return; // Can't shuffle with 0 or 1 videos
  }
  
  let randomIndex;
  do {
    randomIndex = Math.floor(Math.random() * videoQueue.length);
  } while (randomIndex === currentVideoIndex); // Ensure we don't pick the same video
  
  jumpToVideo(randomIndex);
}

/**
 * Get shuffle mode state
 * @returns {boolean} Whether shuffle is enabled
 */
function getShuffleMode() {
  return localStorage.getItem('shuffleMode') === 'true';
}

/**
 * Set shuffle mode state
 * @param {boolean} enabled - Whether to enable shuffle
 */
function setShuffleMode(enabled) {
  localStorage.setItem('shuffleMode', enabled.toString());
}

/**
 * Get repeat mode state
 * @returns {string} Repeat mode: 'off', 'one', 'all'
 */
function getRepeatMode() {
  return localStorage.getItem('repeatMode') || 'off';
}

/**
 * Set repeat mode state
 * @param {string} mode - Repeat mode: 'off', 'one', 'all'
 */
function setRepeatMode(mode) {
  localStorage.setItem('repeatMode', mode);
}

/**
 * Initialize queue controls
 * Set up event listeners and initial state
 */
function initializeQueueControls() {
  // Set up keyboard shortcuts
  document.addEventListener('keydown', handleKeyboardShortcuts);
  
  console.log('🎮 Queue controls initialized');
}

/**
 * Handle keyboard shortcuts for queue controls
 * @param {KeyboardEvent} event - Keyboard event
 */
function handleKeyboardShortcuts(event) {
  // Only handle shortcuts when not typing in input fields
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return;
  }

  switch (event.code) {
    case 'Space':
      event.preventDefault();
      playQueue();
      break;
      
    case 'ArrowLeft':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault();
        previousVideo();
      }
      break;
      
    case 'ArrowRight':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault();
        nextVideo();
      }
      break;
      
    case 'KeyS':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault();
        toggleShuffle();
      }
      break;
      
    case 'KeyR':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault();
        toggleRepeat();
      }
      break;
  }
}

// Initialize controls when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeQueueControls);
} else {
  initializeQueueControls();
}

// Export functions for global access
window.playQueue = playQueue;
window.previousVideo = previousVideo;
window.nextVideo = nextVideo;
window.jumpToVideo = jumpToVideo;
window.toggleShuffle = toggleShuffle;
window.toggleRepeat = toggleRepeat;
window.handleVideoEnd = handleVideoEnd;

console.log('✅ Queue Controls component loaded');
