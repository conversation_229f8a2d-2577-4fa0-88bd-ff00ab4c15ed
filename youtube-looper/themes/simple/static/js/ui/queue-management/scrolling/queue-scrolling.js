// ============================================================================
// QUEUE SCROLLING COMPONENT
// ============================================================================

/**
 * Queue Scrolling Component
 * Handles queue container scrolling, scroll indicators, and scroll controls
 * Manages smooth scrolling and scroll state visualization
 */

/**
 * Update queue scroll indicator
 * Shows/hides scroll buttons based on scroll position
 */
function updateQueueScrollIndicator() {
  const queueContainer = document.getElementById('queue-container');
  const scrollUpBtn = document.getElementById('queue-scroll-up');
  const scrollDownBtn = document.getElementById('queue-scroll-down');

  if (!queueContainer || !scrollUpBtn || !scrollDownBtn) {
    return;
  }

  // Check if we can scroll
  const canScrollUp = queueContainer.scrollTop > 0;
  const canScrollDown = queueContainer.scrollTop < (queueContainer.scrollHeight - queueContainer.clientHeight);

  // Show/hide buttons with smooth transitions
  if (canScrollUp) {
    scrollUpBtn.classList.add('visible');
    scrollUpBtn.setAttribute('aria-hidden', 'false');
  } else {
    scrollUpBtn.classList.remove('visible');
    scrollUpBtn.setAttribute('aria-hidden', 'true');
  }

  if (canScrollDown) {
    scrollDownBtn.classList.add('visible');
    scrollDownBtn.setAttribute('aria-hidden', 'false');
  } else {
    scrollDownBtn.classList.remove('visible');
    scrollDownBtn.setAttribute('aria-hidden', 'true');
  }

  // Update scroll progress indicator
  updateScrollProgress();
}

/**
 * Update scroll progress indicator
 */
function updateScrollProgress() {
  const queueContainer = document.getElementById('queue-container');
  const progressIndicator = document.getElementById('queue-scroll-progress');

  if (!queueContainer || !progressIndicator) {
    return;
  }

  const scrollTop = queueContainer.scrollTop;
  const scrollHeight = queueContainer.scrollHeight - queueContainer.clientHeight;
  
  if (scrollHeight <= 0) {
    progressIndicator.style.display = 'none';
    return;
  }

  const scrollPercentage = (scrollTop / scrollHeight) * 100;
  progressIndicator.style.display = 'block';
  progressIndicator.style.height = `${scrollPercentage}%`;
}

/**
 * Scroll queue up
 * @param {number} amount - Amount to scroll (default: 60px)
 */
function scrollQueueUp(amount = 60) {
  const queueContainer = document.getElementById('queue-container');
  
  if (!queueContainer) {
    return;
  }

  queueContainer.scrollBy({
    top: -amount,
    behavior: 'smooth'
  });

  // Update indicators after scroll animation
  setTimeout(updateQueueScrollIndicator, 150);
}

/**
 * Scroll queue down
 * @param {number} amount - Amount to scroll (default: 60px)
 */
function scrollQueueDown(amount = 60) {
  const queueContainer = document.getElementById('queue-container');
  
  if (!queueContainer) {
    return;
  }

  queueContainer.scrollBy({
    top: amount,
    behavior: 'smooth'
  });

  // Update indicators after scroll animation
  setTimeout(updateQueueScrollIndicator, 150);
}

/**
 * Scroll to top of queue
 */
function scrollQueueToTop() {
  const queueContainer = document.getElementById('queue-container');
  
  if (!queueContainer) {
    return;
  }

  queueContainer.scrollTo({
    top: 0,
    behavior: 'smooth'
  });

  setTimeout(updateQueueScrollIndicator, 300);
}

/**
 * Scroll to bottom of queue
 */
function scrollQueueToBottom() {
  const queueContainer = document.getElementById('queue-container');
  
  if (!queueContainer) {
    return;
  }

  queueContainer.scrollTo({
    top: queueContainer.scrollHeight,
    behavior: 'smooth'
  });

  setTimeout(updateQueueScrollIndicator, 300);
}

/**
 * Scroll to specific queue item
 * @param {number} index - Index of item to scroll to
 * @param {string} position - Position ('start', 'center', 'end', 'nearest')
 */
function scrollToQueueItem(index, position = 'center') {
  const queueContainer = document.getElementById('queue-container');
  const item = document.querySelector(`.queue-item:nth-child(${index + 1})`);
  
  if (!queueContainer || !item) {
    return;
  }

  // Calculate scroll position based on desired position
  let scrollTop;
  const containerHeight = queueContainer.clientHeight;
  const itemTop = item.offsetTop - queueContainer.offsetTop;
  const itemHeight = item.offsetHeight;

  switch (position) {
    case 'start':
      scrollTop = itemTop;
      break;
    case 'end':
      scrollTop = itemTop - containerHeight + itemHeight;
      break;
    case 'center':
      scrollTop = itemTop - (containerHeight / 2) + (itemHeight / 2);
      break;
    case 'nearest':
    default:
      const currentScrollTop = queueContainer.scrollTop;
      const itemBottom = itemTop + itemHeight;
      const containerBottom = currentScrollTop + containerHeight;

      if (itemTop < currentScrollTop) {
        // Item is above visible area
        scrollTop = itemTop;
      } else if (itemBottom > containerBottom) {
        // Item is below visible area
        scrollTop = itemBottom - containerHeight;
      } else {
        // Item is already visible
        return;
      }
      break;
  }

  queueContainer.scrollTo({
    top: Math.max(0, scrollTop),
    behavior: 'smooth'
  });

  setTimeout(updateQueueScrollIndicator, 300);
}

/**
 * Handle mouse wheel events on queue container
 * @param {WheelEvent} event - Wheel event
 */
function handleQueueWheel(event) {
  const queueContainer = document.getElementById('queue-container');
  
  if (!queueContainer) {
    return;
  }

  // Check if we can scroll in the requested direction
  const scrollTop = queueContainer.scrollTop;
  const scrollHeight = queueContainer.scrollHeight - queueContainer.clientHeight;
  
  const scrollingUp = event.deltaY < 0;
  const scrollingDown = event.deltaY > 0;
  
  const canScrollUp = scrollTop > 0;
  const canScrollDown = scrollTop < scrollHeight;

  // If we can't scroll in the requested direction, prevent default
  if ((scrollingUp && !canScrollUp) || (scrollingDown && !canScrollDown)) {
    event.preventDefault();
    return;
  }

  // Update indicators after scroll
  setTimeout(updateQueueScrollIndicator, 50);
}

/**
 * Handle touch events for mobile scrolling
 * @param {TouchEvent} event - Touch event
 */
function handleQueueTouch(event) {
  // Allow native touch scrolling
  setTimeout(updateQueueScrollIndicator, 100);
}

/**
 * Initialize queue scroll controls
 */
function initializeQueueScrollControls() {
  const scrollUpBtn = document.getElementById('queue-scroll-up');
  const scrollDownBtn = document.getElementById('queue-scroll-down');
  const queueContainer = document.getElementById('queue-container');

  // Set up scroll button event listeners
  if (scrollUpBtn) {
    scrollUpBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      scrollQueueUp();
    });

    // Add keyboard support
    scrollUpBtn.addEventListener('keydown', function(e) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        scrollQueueUp();
      }
    });
  }

  if (scrollDownBtn) {
    scrollDownBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      scrollQueueDown();
    });

    // Add keyboard support
    scrollDownBtn.addEventListener('keydown', function(e) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        scrollQueueDown();
      }
    });
  }

  // Set up queue container event listeners
  if (queueContainer) {
    // Update scroll indicators when user scrolls manually
    queueContainer.addEventListener('scroll', updateQueueScrollIndicator);

    // Handle wheel events
    queueContainer.addEventListener('wheel', handleQueueWheel, { passive: false });

    // Handle touch events
    queueContainer.addEventListener('touchstart', handleQueueTouch, { passive: true });
    queueContainer.addEventListener('touchmove', handleQueueTouch, { passive: true });
    queueContainer.addEventListener('touchend', handleQueueTouch, { passive: true });

    // Handle keyboard navigation
    queueContainer.addEventListener('keydown', function(e) {
      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          scrollQueueUp(30);
          break;
        case 'ArrowDown':
          e.preventDefault();
          scrollQueueDown(30);
          break;
        case 'PageUp':
          e.preventDefault();
          scrollQueueUp(queueContainer.clientHeight * 0.8);
          break;
        case 'PageDown':
          e.preventDefault();
          scrollQueueDown(queueContainer.clientHeight * 0.8);
          break;
        case 'Home':
          e.preventDefault();
          scrollQueueToTop();
          break;
        case 'End':
          e.preventDefault();
          scrollQueueToBottom();
          break;
      }
    });
  }

  // Initial update
  setTimeout(updateQueueScrollIndicator, 100);

  console.log('📜 Queue scrolling controls initialized');
}

/**
 * Auto-scroll to currently playing item
 */
function autoScrollToCurrentItem() {
  const currentVideoIndex = getCurrentVideoIndex();
  const videoQueue = getVideoQueue();

  if (currentVideoIndex >= 0 && currentVideoIndex < videoQueue.length) {
    scrollToQueueItem(currentVideoIndex, 'nearest');
  }
}

// Initialize scroll controls when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeQueueScrollControls);
} else {
  // Delay initialization to ensure all elements are available
  setTimeout(initializeQueueScrollControls, 100);
}

// Export functions for global access
window.updateQueueScrollIndicator = updateQueueScrollIndicator;
window.scrollQueueUp = scrollQueueUp;
window.scrollQueueDown = scrollQueueDown;
window.scrollQueueToTop = scrollQueueToTop;
window.scrollQueueToBottom = scrollQueueToBottom;
window.scrollToQueueItem = scrollToQueueItem;
window.autoScrollToCurrentItem = autoScrollToCurrentItem;

console.log('✅ Queue Scrolling component loaded');
