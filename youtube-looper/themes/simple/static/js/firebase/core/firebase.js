// ============================================================================
// FIREBASE CONFIGURATION AND INITIALIZATION
// ============================================================================

// Global Firebase variables
let firebaseInitialized = false;
let firebaseConfig = null;
let currentUser = null;

/**
 * Load Firebase configuration from external file
 * @returns {Promise<boolean>} - True if config loaded successfully
 */
async function loadFirebaseConfig() {
  try {
    const response = await fetch('firebase-config.json');
    if (response.ok) {
      firebaseConfig = await response.json();
      console.log('✅ Firebase config loaded');
      return true;
    }
  } catch (error) {
    console.warn('⚠️ Firebase config file not found');
  }
  return false;
}

/**
 * Initialize Firebase when scripts are loaded
 * @returns {Promise<boolean>} - True if Firebase initialized successfully
 */
async function initializeFirebaseWhenReady() {
  // Wait for Firebase scripts to load
  let attempts = 0;
  const maxAttempts = 50;

  while (attempts < maxAttempts) {
    if (typeof firebase !== 'undefined' && firebaseConfig) {
      try {
        firebase.initializeApp(firebaseConfig);
        window.db = firebase.firestore();
        window.auth = firebase.auth();

        // Enable offline persistence for better UX
        try {
          await firebase.firestore().enablePersistence({
            synchronizeTabs: true
          });
          console.log('✅ Firebase offline persistence enabled');
        } catch (persistenceError) {
          if (persistenceError.code === 'failed-precondition') {
            console.warn('⚠️ Firebase persistence failed: Multiple tabs open');
          } else if (persistenceError.code === 'unimplemented') {
            console.warn('⚠️ Firebase persistence not supported in this browser');
          } else if (persistenceError.code === 'unavailable') {
            console.warn('⚠️ Firebase persistence unavailable: IndexedDB error. Falling back to memory cache.');
            console.warn('💡 Try clearing browser storage or using a different browser/incognito mode');
          } else {
            console.warn('⚠️ Firebase persistence error:', persistenceError);
          }
          // Continue without persistence - app will still work with memory cache
          console.log('📱 App will continue with memory-only cache (no offline support)');
        }

        // Set up auth state listener
        firebase.auth().onAuthStateChanged((user) => {
          currentUser = user;
          if (user) {
            console.log('🔐 User authenticated:', user.uid);
          } else {
            console.log('🔓 User not authenticated');
          }
        });

        firebaseInitialized = true;
        console.log('🔥 Firebase initialized successfully');
        return true;
      } catch (error) {
        console.error('❌ Error initializing Firebase:', error);
        return false;
      }
    }
    await new Promise(resolve => setTimeout(resolve, 100));
    attempts++;
  }

  console.warn('⚠️ Firebase initialization timeout');
  return false;
}

/**
 * Load Firebase scripts dynamically
 */
function loadFirebaseScripts() {
  // Load Firebase App script
  const firebaseAppScript = document.createElement('script');
  firebaseAppScript.src = "https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js";
  document.head.appendChild(firebaseAppScript);

  // Load Firebase Firestore script
  const firebaseFirestoreScript = document.createElement('script');
  firebaseFirestoreScript.src = "https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js";
  document.head.appendChild(firebaseFirestoreScript);

  // Load Firebase Auth script
  const firebaseAuthScript = document.createElement('script');
  firebaseAuthScript.src = "https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js";
  document.head.appendChild(firebaseAuthScript);

  console.log('📦 Firebase scripts loading...');
}

/**
 * Check if Firebase is initialized
 * @returns {boolean} - True if Firebase is initialized
 */
function isFirebaseInitialized() {
  return firebaseInitialized;
}

/**
 * Get Firebase database instance
 * @returns {Object|null} - Firebase database instance or null
 */
function getFirebaseDb() {
  return window.db || null;
}

/**
 * Get Firebase auth instance
 * @returns {Object|null} - Firebase auth instance or null
 */
function getFirebaseAuth() {
  return window.auth || null;
}

/**
 * Get current authenticated user
 * @returns {Object|null} - Current user or null
 */
function getCurrentUser() {
  return currentUser;
}

/**
 * Sign in anonymously
 * @returns {Promise<Object|null>} - User object or null
 */
async function signInAnonymously() {
  if (!isFirebaseInitialized()) {
    console.warn('Firebase not initialized');
    return null;
  }

  try {
    const auth = getFirebaseAuth();
    const result = await auth.signInAnonymously();
    console.log('🔐 Anonymous sign-in successful:', result.user.uid);
    return result.user;
  } catch (error) {
    console.error('❌ Anonymous sign-in failed:', error);
    return null;
  }
}

/**
 * Get user ID for queue storage
 * @returns {Promise<string|null>} - User ID or null
 */
async function getUserId() {
  let user = getCurrentUser();

  if (!user) {
    // Try to sign in anonymously
    user = await signInAnonymously();
  }

  return user ? user.uid : null;
}

/**
 * Initialize Firebase module
 * @returns {Promise<boolean>} - True if initialization successful
 */
async function initializeFirebase() {
  console.log('🔥 Initializing Firebase...');
  
  // Load Firebase scripts
  loadFirebaseScripts();
  
  // Load Firebase configuration
  const configLoaded = await loadFirebaseConfig();
  if (configLoaded) {
    await initializeFirebaseWhenReady();
    return true;
  } else {
    console.warn('⚠️ Firebase configuration not found. Queue sharing features will be disabled.');
    return false;
  }
}

console.log('✅ Firebase module loaded');
