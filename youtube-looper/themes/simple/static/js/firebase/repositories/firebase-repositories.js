// ============================================================================
// FIREBASE REPOSITORY CLASSES
// ============================================================================

/**
 * Repository for public shared queues
 */
class PublicQueueRepository extends FirebaseRepository {
  constructor() {
    super(PublicQueueModel.COLLECTION);
  }

  /**
   * Save a public queue
   * @param {PublicQueueModel} queueModel - Queue model to save
   * @returns {Promise<string>} Queue ID
   */
  async save(queueModel) {
    const docRef = queueModel.id ? 
      this.getDoc(queueModel.id) : 
      this.getCollection().doc();
    
    const data = queueModel.toFirebaseData();
    
    // Add server timestamp
    data[QueueMetadataModel.FIELDS.LAST_MODIFIED] = firebase.firestore.FieldValue.serverTimestamp();
    
    // Set creation timestamp if new
    if (!queueModel.id) {
      data[QueueMetadataModel.FIELDS.CREATED_AT] = firebase.firestore.FieldValue.serverTimestamp();
      queueModel.id = docRef.id;
    }

    await docRef.set(data);
    queueModel.markSaved();
    
    return docRef.id;
  }

  /**
   * Load a public queue by ID
   * @param {string} queueId - Queue ID
   * @returns {Promise<PublicQueueModel|null>} Queue model or null
   */
  async load(queueId) {
    const docSnapshot = await this.getDoc(queueId).get();
    
    if (!docSnapshot.exists) {
      return null;
    }

    return PublicQueueModel.fromFirebaseDoc(docSnapshot);
  }

  /**
   * Delete a public queue
   * @param {string} queueId - Queue ID
   * @returns {Promise<void>}
   */
  async delete(queueId) {
    await this.getDoc(queueId).delete();
  }

  /**
   * List public queues with sorting and pagination
   * @param {Object} options - Query options
   * @returns {Promise<Array<PublicQueueModel>>} Array of queue models
   */
  async list(options = {}) {
    const {
      sortBy = 'recent',
      limit = 50,
      startAfter = null
    } = options;

    let query = this.getCollection();

    // Apply sorting
    switch (sortBy) {
      case 'popular':
        query = query.orderBy(`${QueueMetadataModel.FIELDS.VIEW_COUNT}`, 'desc');
        break;
      case 'recent':
        query = query.orderBy(`${QueueMetadataModel.FIELDS.LAST_MODIFIED}`, 'desc');
        break;
      case 'newest':
        query = query.orderBy(`${QueueMetadataModel.FIELDS.CREATED_AT}`, 'desc');
        break;
      case 'longest':
        query = query.orderBy(`${QueueMetadataModel.FIELDS.TOTAL_DURATION}`, 'desc');
        break;
      default:
        query = query.orderBy(`${QueueMetadataModel.FIELDS.LAST_MODIFIED}`, 'desc');
    }

    // Apply pagination
    if (startAfter) {
      query = query.startAfter(startAfter);
    }

    query = query.limit(limit);

    const querySnapshot = await query.get();
    const queues = [];

    querySnapshot.forEach((doc) => {
      queues.push(PublicQueueModel.fromFirebaseDoc(doc));
    });

    return queues;
  }

  /**
   * Increment view count for a queue
   * @param {string} queueId - Queue ID
   * @returns {Promise<void>}
   */
  async incrementViewCount(queueId) {
    await this.getDoc(queueId).update({
      [`${QueueMetadataModel.FIELDS.VIEW_COUNT}`]: firebase.firestore.FieldValue.increment(1)
    });
  }

  /**
   * Update queue metadata only
   * @param {string} queueId - Queue ID
   * @param {QueueMetadataModel} metadata - Metadata to update
   * @returns {Promise<void>}
   */
  async updateMetadata(queueId, metadata) {
    const updateData = {};
    updateData[`${QueueMetadataModel.FIELDS.LAST_MODIFIED}`] = firebase.firestore.FieldValue.serverTimestamp();
    
    // Add metadata fields with proper paths
    Object.keys(metadata.toObject()).forEach(key => {
      if (key !== QueueMetadataModel.FIELDS.CREATED_AT) { // Don't update creation date
        updateData[`metadata.${key}`] = metadata.get(key);
      }
    });

    await this.getDoc(queueId).update(updateData);
  }
}

/**
 * Repository for personal queues
 */
class PersonalQueueRepository extends FirebaseRepository {
  constructor() {
    super(PersonalQueueModel.COLLECTION);
  }

  /**
   * Save a personal queue
   * @param {PersonalQueueModel} queueModel - Queue model to save
   * @returns {Promise<string>} Queue ID
   */
  async save(queueModel) {
    const docRef = queueModel.id ? 
      this.getDoc(queueModel.id) : 
      this.getCollection().doc();
    
    const data = queueModel.toFirebaseData();
    
    // Add server timestamp
    data[PersonalQueueModel.FIELDS.LAST_MODIFIED] = firebase.firestore.FieldValue.serverTimestamp();
    
    // Set creation timestamp if new
    if (!queueModel.id) {
      data[PersonalQueueModel.FIELDS.CREATED_AT] = firebase.firestore.FieldValue.serverTimestamp();
      queueModel.id = docRef.id;
    }

    await docRef.set(data);
    queueModel.markSaved();
    
    return docRef.id;
  }

  /**
   * Load a personal queue by ID
   * @param {string} queueId - Queue ID
   * @returns {Promise<PersonalQueueModel|null>} Queue model or null
   */
  async load(queueId) {
    const docSnapshot = await this.getDoc(queueId).get();
    
    if (!docSnapshot.exists) {
      return null;
    }

    return PersonalQueueModel.fromFirebaseDoc(docSnapshot);
  }

  /**
   * Load personal queue for user (legacy single queue per user)
   * @param {string} userId - User ID
   * @returns {Promise<PersonalQueueModel|null>} Queue model or null
   */
  async loadForUser(userId) {
    const docSnapshot = await this.getDoc(userId).get();
    
    if (!docSnapshot.exists) {
      return null;
    }

    return PersonalQueueModel.fromFirebaseDoc(docSnapshot);
  }

  /**
   * List personal queues for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array<PersonalQueueModel>>} Array of queue models
   */
  async listForUser(userId, options = {}) {
    const {
      limit = 50,
      orderBy = PersonalQueueModel.FIELDS.LAST_MODIFIED,
      direction = 'desc'
    } = options;

    let query = this.getCollection()
      .where(PersonalQueueModel.FIELDS.USER_ID, '==', userId)
      .orderBy(orderBy, direction)
      .limit(limit);

    const querySnapshot = await query.get();
    const queues = [];

    querySnapshot.forEach((doc) => {
      queues.push(PersonalQueueModel.fromFirebaseDoc(doc));
    });

    return queues;
  }

  /**
   * Delete a personal queue
   * @param {string} queueId - Queue ID
   * @returns {Promise<void>}
   */
  async delete(queueId) {
    await this.getDoc(queueId).delete();
  }

  /**
   * Save personal queue for user (legacy single queue per user)
   * @param {string} userId - User ID
   * @param {QueueDataModel} queueData - Queue data
   * @returns {Promise<void>}
   */
  async saveForUser(userId, queueData) {
    const data = {
      [PersonalQueueModel.FIELDS.QUEUE_DATA]: queueData.toObject(),
      [PersonalQueueModel.FIELDS.LAST_MODIFIED]: firebase.firestore.FieldValue.serverTimestamp()
    };

    await this.getDoc(userId).set(data);
  }
}

// Global repository instances
window.publicQueueRepo = new PublicQueueRepository();
window.personalQueueRepo = new PersonalQueueRepository();

console.log('✅ Firebase Repositories module loaded');
